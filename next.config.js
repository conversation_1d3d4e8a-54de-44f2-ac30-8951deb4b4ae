/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export', // Outputs a Single-Page Application (SPA).
  rewrites() {
    return {
      fallback: [
        {
          // On dev and production it is done by DigitalOcean routing
          source: "/api/:path*",
          destination: "http://localhost:8000/:path*",
        },
      ],
    };
  },
}
 
module.exports = nextConfig
