{"compilerOptions": {"lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "plugins": [{"name": "next"}], "target": "es2017", "forceConsistentCasingInFileNames": true, "alwaysStrict": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", ".next/dev/types/**/*.ts"], "exclude": ["node_modules"]}