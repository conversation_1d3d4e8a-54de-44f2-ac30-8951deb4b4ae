{"name": "scopezilla", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "next build", "start": "concurrently --raw 'next dev' 'docker compose up'", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "engines": {"node": "24.2.0", "npm": "11.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.39.0", "@types/node": "24.10.0", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.2", "concurrently": "^9.2.1", "eslint-config-next": "^16.0.1", "supabase": "^2.54.11", "typescript": "^5.9.3"}, "dependencies": {"next": "^16.0.1"}}